<?php
/**
 * Plugin Name:       Orbit Addons for CF7
 * Plugin URI:        https://github.com/sadmankabiir/la-addons
 * Description:       A comprehensive, all-in-one solution for managing form submissions, creating dynamic forms with conditional logic, capturing digital signatures, and seamlessly integrating with the WordPress Media Library.
 * Version:           1.0
 * Requires at least: 5.2
 * Requires PHP:      7.2
 * Author:            <PERSON><PERSON>
 * Author URI:        https://github.com/sadmankabiir
 * License:           GPL v2 or later
 * License URI:       https://www.gnu.org/licenses/gpl-2.0.html
 * Update URI:        https://github.com/sadmankabiir/la-addons
 * Text Domain:       la-addons
 * Domain Path:       /languages
 */

// --- FILE INCLUDES ---
require_once plugin_dir_path( __FILE__ ) . 'includes/database.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/submission-handler.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/enqueue-scripts.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/digital-signature.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/conditional-fields-api.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/drag-drop-upload.php';

// --- ADMIN-ONLY INCLUDES ---
if ( is_admin() ) {
    require_once plugin_dir_path( __FILE__ ) . 'admin/admin-menu.php';
    require_once plugin_dir_path( __FILE__ ) . 'admin/conditional-fields-ui.php';
    require_once plugin_dir_path( __FILE__ ) . 'admin/signature-options.php';
    require_once plugin_dir_path( __FILE__ ) . 'admin/dragdrop-upload-options.php';
}

// --- ACTIVATION HOOK ---
register_activation_hook( __FILE__, 'la_addons_activate' );




