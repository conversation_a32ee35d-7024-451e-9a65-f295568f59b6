<?php
/**
 * Plugin Name:       Orbit Addons for CF7
 * Plugin URI:        https://github.com/sadmankabiir/la-addons
 * Description:       A comprehensive, all-in-one solution for managing form submissions, creating dynamic forms with conditional logic, capturing digital signatures, and seamlessly integrating with the WordPress Media Library.
 * Version:           1.0
 * Requires at least: 5.2
 * Requires PHP:      7.2
 * Author:            <PERSON><PERSON>
 * Author URI:        https://github.com/sadmankabiir
 * License:           GPL v2 or later
 * License URI:       https://www.gnu.org/licenses/gpl-2.0.html
 * Update URI:        https://github.com/sadmankabiir/la-addons
 * Text Domain:       la-addons
 * Domain Path:       /languages
 */

// --- FILE INCLUDES ---
require_once plugin_dir_path( __FILE__ ) . 'includes/database.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/submission-handler.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/enqueue-scripts.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/digital-signature.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/conditional-fields-api.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/drag-drop-upload.php';

// --- ADMIN-ONLY INCLUDES ---
if ( is_admin() ) {
    require_once plugin_dir_path( __FILE__ ) . 'admin/admin-menu.php';
    require_once plugin_dir_path( __FILE__ ) . 'admin/conditional-fields-ui.php';
    require_once plugin_dir_path( __FILE__ ) . 'admin/signature-options.php';
    require_once plugin_dir_path( __FILE__ ) . 'admin/dragdrop-upload-options.php';
}

// Diagnostic function to check CF7 availability
function la_addons_check_cf7_status() {
    error_log( 'LA Addons: Checking CF7 status...' );

    // Include plugin.php if not already included
    if ( ! function_exists( 'is_plugin_active' ) ) {
        include_once( ABSPATH . 'wp-admin/includes/plugin.php' );
    }

    // Check if CF7 plugin is active
    if ( ! is_plugin_active( 'contact-form-7/wp-contact-form-7.php' ) ) {
        error_log( 'LA Addons: Contact Form 7 plugin is NOT active' );
        return;
    }

    error_log( 'LA Addons: Contact Form 7 plugin is active' );

    if ( function_exists( 'wpcf7_add_form_tag' ) ) {
        error_log( 'LA Addons: Contact Form 7 functions are available' );

        // Try to register a simple test tag with a proper callback
        wpcf7_add_form_tag( 'la-test', 'la_addons_test_tag_handler' );

        error_log( 'LA Addons: Test tag registered successfully' );
    } else {
        error_log( 'LA Addons: Contact Form 7 functions NOT available' );

        // List available CF7 functions for debugging
        $cf7_functions = array( 'wpcf7_add_form_tag', 'wpcf7_get_current_contact_form', 'wpcf7_form_controls_class' );
        foreach ( $cf7_functions as $func ) {
            error_log( 'LA Addons: Function ' . $func . ' exists: ' . ( function_exists( $func ) ? 'YES' : 'NO' ) );
        }
    }
}

// Simple test tag handler
function la_addons_test_tag_handler( $tag ) {
    error_log( 'LA Addons: Test tag handler called!' );
    return '<p style="background: yellow; padding: 10px; border: 2px solid orange;">LA Addons Test Tag Working!</p>';
}

// Hook to multiple actions to ensure we catch CF7 when it's ready
add_action( 'plugins_loaded', 'la_addons_check_cf7_status', 999 );
add_action( 'init', 'la_addons_check_cf7_status', 999 );
add_action( 'wpcf7_init', 'la_addons_check_cf7_status', 999 );

// Modern CF7 registration - using the correct timing and method
add_action( 'wpcf7_init', function() {
    error_log( 'LA Addons: Modern CF7 registration on wpcf7_init' );

    if ( function_exists( 'wpcf7_add_form_tag' ) ) {
        error_log( 'LA Addons: wpcf7_add_form_tag function available' );

        // Register test tag with proper parameters
        wpcf7_add_form_tag(
            'working-test',
            'la_addons_working_test_handler',
            array(
                'name-attr' => false
            )
        );

        // Register our dragdrop tag
        wpcf7_add_form_tag(
            array( 'dragdrop-upload', 'dragdrop-upload*' ),
            'la_addons_dragdrop_upload_form_tag_handler',
            array(
                'name-attr' => true
            )
        );

        error_log( 'LA Addons: Tags registered successfully on wpcf7_init' );
    } else {
        error_log( 'LA Addons: wpcf7_add_form_tag function NOT available on wpcf7_init' );
    }
}, 10 );

// Working test handler
function la_addons_working_test_handler( $tag ) {
    error_log( 'LA Addons: Working test handler called!' );
    return '<div style="background: green; color: white; padding: 15px; border-radius: 5px; margin: 10px 0;">
        <strong>✅ SUCCESS!</strong> CF7 custom tags are working!<br>
        <small>This proves wpcf7_add_form_tag works correctly.</small>
    </div>';
}

// Force clear CF7 cache and check form processing
add_action( 'wp_loaded', function() {
    // Clear any CF7 caches
    if ( function_exists( 'wpcf7_clear_cache' ) ) {
        wpcf7_clear_cache();
        error_log( 'LA Addons: CF7 cache cleared' );
    }

    // Check if forms are being processed
    if ( class_exists( 'WPCF7_ContactForm' ) ) {
        $forms = WPCF7_ContactForm::find();
        error_log( 'LA Addons: Found ' . count( $forms ) . ' CF7 forms' );

        foreach ( $forms as $form ) {
            $form_content = $form->prop( 'form' );
            if ( strpos( $form_content, 'test-simple' ) !== false ) {
                error_log( 'LA Addons: Found test-simple in form ID: ' . $form->id() );

                // Try to scan the form tags
                $tags = $form->scan_form_tags();
                error_log( 'LA Addons: Form has ' . count( $tags ) . ' tags total' );

                foreach ( $tags as $tag ) {
                    error_log( 'LA Addons: Found tag type: ' . $tag->type );
                }
            }
        }
    }
}, 1000 );

// Remove duplicate registrations - now handled in the wpcf7_init hook above

// --- ACTIVATION HOOK ---
register_activation_hook( __FILE__, 'la_addons_activate' );




