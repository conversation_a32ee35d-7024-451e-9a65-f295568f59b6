<?php
/**
 * Plugin Name:       Orbit Addons for CF7
 * Plugin URI:        https://github.com/sadmankabiir/la-addons
 * Description:       A comprehensive, all-in-one solution for managing form submissions, creating dynamic forms with conditional logic, capturing digital signatures, and seamlessly integrating with the WordPress Media Library.
 * Version:           1.0
 * Requires at least: 5.2
 * Requires PHP:      7.2
 * Author:            <PERSON><PERSON>
 * Author URI:        https://github.com/sadmankabiir
 * License:           GPL v2 or later
 * License URI:       https://www.gnu.org/licenses/gpl-2.0.html
 * Update URI:        https://github.com/sadmankabiir/la-addons
 * Text Domain:       la-addons
 * Domain Path:       /languages
 */

// --- FILE INCLUDES ---
require_once plugin_dir_path( __FILE__ ) . 'includes/database.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/submission-handler.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/enqueue-scripts.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/digital-signature.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/conditional-fields-api.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/drag-drop-upload.php';

// --- ADMIN-ONLY INCLUDES ---
if ( is_admin() ) {
    require_once plugin_dir_path( __FILE__ ) . 'admin/admin-menu.php';
    require_once plugin_dir_path( __FILE__ ) . 'admin/conditional-fields-ui.php';
    require_once plugin_dir_path( __FILE__ ) . 'admin/signature-options.php';
    require_once plugin_dir_path( __FILE__ ) . 'admin/dragdrop-upload-options.php';
}

// Diagnostic function to check CF7 availability
function la_addons_check_cf7_status() {
    error_log( 'LA Addons: Checking CF7 status...' );

    // Include plugin.php if not already included
    if ( ! function_exists( 'is_plugin_active' ) ) {
        include_once( ABSPATH . 'wp-admin/includes/plugin.php' );
    }

    // Check if CF7 plugin is active
    if ( ! is_plugin_active( 'contact-form-7/wp-contact-form-7.php' ) ) {
        error_log( 'LA Addons: Contact Form 7 plugin is NOT active' );
        return;
    }

    error_log( 'LA Addons: Contact Form 7 plugin is active' );

    if ( function_exists( 'wpcf7_add_form_tag' ) ) {
        error_log( 'LA Addons: Contact Form 7 functions are available' );

        // Try to register a simple test tag with a proper callback
        wpcf7_add_form_tag( 'la-test', 'la_addons_test_tag_handler' );

        error_log( 'LA Addons: Test tag registered successfully' );
    } else {
        error_log( 'LA Addons: Contact Form 7 functions NOT available' );

        // List available CF7 functions for debugging
        $cf7_functions = array( 'wpcf7_add_form_tag', 'wpcf7_get_current_contact_form', 'wpcf7_form_controls_class' );
        foreach ( $cf7_functions as $func ) {
            error_log( 'LA Addons: Function ' . $func . ' exists: ' . ( function_exists( $func ) ? 'YES' : 'NO' ) );
        }
    }
}

// Simple test tag handler
function la_addons_test_tag_handler( $tag ) {
    error_log( 'LA Addons: Test tag handler called!' );
    return '<p style="background: yellow; padding: 10px; border: 2px solid orange;">LA Addons Test Tag Working!</p>';
}

// Hook to multiple actions to ensure we catch CF7 when it's ready
add_action( 'plugins_loaded', 'la_addons_check_cf7_status', 999 );
add_action( 'init', 'la_addons_check_cf7_status', 999 );
add_action( 'wpcf7_init', 'la_addons_check_cf7_status', 999 );

// Direct registration attempt
function la_addons_direct_dragdrop_registration() {
    if ( function_exists( 'wpcf7_add_form_tag' ) ) {
        error_log( 'LA Addons: Attempting direct dragdrop registration' );

        wpcf7_add_form_tag(
            'dragdrop-upload',
            'la_addons_dragdrop_upload_form_tag_handler',
            array( 'name-attr' => true )
        );

        wpcf7_add_form_tag(
            'dragdrop-upload*',
            'la_addons_dragdrop_upload_form_tag_handler',
            array( 'name-attr' => true )
        );

        error_log( 'LA Addons: Direct dragdrop registration completed' );
    }
}

// Try registration on multiple hooks
add_action( 'wpcf7_init', 'la_addons_direct_dragdrop_registration', 10 );
add_action( 'init', 'la_addons_direct_dragdrop_registration', 20 );
add_action( 'wp_loaded', 'la_addons_direct_dragdrop_registration', 30 );

// --- ACTIVATION HOOK ---
register_activation_hook( __FILE__, 'la_addons_activate' );




