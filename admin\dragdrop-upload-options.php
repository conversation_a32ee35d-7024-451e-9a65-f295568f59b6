<?php
/**
 * Drag-and-Drop Upload Options Panel for Contact Form 7
 * Provides admin interface for configuring drag-and-drop upload settings
 */

// Block direct access to the file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Add drag-and-drop upload options tab to CF7 form editor
 */
function la_addons_add_dragdrop_upload_panel( $panels ) {
    $panels['dragdrop-upload-options'] = array(
        'title' => __( 'Drag & Drop Upload', 'la-addons' ),
        'callback' => 'la_addons_dragdrop_upload_panel_content'
    );
    return $panels;
}
add_filter( 'wpcf7_editor_panels', 'la_addons_add_dragdrop_upload_panel' );

/**
 * Render the drag-and-drop upload options panel content
 */
function la_addons_dragdrop_upload_panel_content( $post ) {
    $options = get_post_meta( $post->id(), '_la_addons_dragdrop_upload_options', true );
    
    // Default options
    $defaults = array(
        'max_files' => 5,
        'max_file_size' => '10MB',
        'allowed_types' => 'image/*,application/pdf,.doc,.docx,.txt',
        'auto_upload' => true,
        'show_previews' => true,
        'enable_progress' => true,
        'upload_path' => 'default', // default, custom
        'custom_path' => '',
        'file_naming' => 'original', // original, timestamp, custom
        'custom_naming_pattern' => '{name}-{timestamp}',
        'security_scan' => true,
        'duplicate_handling' => 'rename' // rename, replace, skip
    );
    
    $options = wp_parse_args( $options, $defaults );
    ?>
    
    <div class="la-addons-panel">
        <h2><?php echo esc_html( __( 'Drag & Drop Upload Configuration', 'la-addons' ) ); ?></h2>
        <p class="description">
            <?php echo esc_html( __( 'Configure global settings for drag-and-drop file upload fields in this form.', 'la-addons' ) ); ?>
        </p>
        
        <table class="form-table">
            <tbody>
                <!-- File Limits -->
                <tr>
                    <th scope="row">
                        <label for="dragdrop_max_files"><?php echo esc_html( __( 'Maximum Files', 'la-addons' ) ); ?></label>
                    </th>
                    <td>
                        <input type="number" 
                               id="dragdrop_max_files" 
                               name="la_addons_dragdrop_upload_options[max_files]" 
                               value="<?php echo esc_attr( $options['max_files'] ); ?>" 
                               min="1" 
                               max="50" 
                               class="small-text" />
                        <p class="description"><?php echo esc_html( __( 'Maximum number of files that can be uploaded per field (1-50).', 'la-addons' ) ); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="dragdrop_max_file_size"><?php echo esc_html( __( 'Maximum File Size', 'la-addons' ) ); ?></label>
                    </th>
                    <td>
                        <input type="text" 
                               id="dragdrop_max_file_size" 
                               name="la_addons_dragdrop_upload_options[max_file_size]" 
                               value="<?php echo esc_attr( $options['max_file_size'] ); ?>" 
                               class="regular-text" />
                        <p class="description"><?php echo esc_html( __( 'Maximum file size (e.g., 10MB, 5MB, 2048KB). Server limit: ', 'la-addons' ) . size_format( wp_max_upload_size() ) ); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="dragdrop_allowed_types"><?php echo esc_html( __( 'Allowed File Types', 'la-addons' ) ); ?></label>
                    </th>
                    <td>
                        <textarea id="dragdrop_allowed_types" 
                                  name="la_addons_dragdrop_upload_options[allowed_types]" 
                                  rows="3" 
                                  class="large-text"><?php echo esc_textarea( $options['allowed_types'] ); ?></textarea>
                        <p class="description">
                            <?php echo esc_html( __( 'Comma-separated list of allowed file types. Examples:', 'la-addons' ) ); ?><br>
                            <code>image/*</code> - <?php echo esc_html( __( 'All image types', 'la-addons' ) ); ?><br>
                            <code>.pdf,.doc,.docx</code> - <?php echo esc_html( __( 'Specific extensions', 'la-addons' ) ); ?><br>
                            <code>application/pdf</code> - <?php echo esc_html( __( 'Specific MIME types', 'la-addons' ) ); ?>
                        </p>
                    </td>
                </tr>
                
                <!-- Upload Behavior -->
                <tr>
                    <th scope="row"><?php echo esc_html( __( 'Upload Behavior', 'la-addons' ) ); ?></th>
                    <td>
                        <fieldset>
                            <label>
                                <input type="checkbox" 
                                       name="la_addons_dragdrop_upload_options[auto_upload]" 
                                       value="1" 
                                       <?php checked( $options['auto_upload'] ); ?> />
                                <?php echo esc_html( __( 'Auto-upload files when selected', 'la-addons' ) ); ?>
                            </label>
                            <p class="description"><?php echo esc_html( __( 'Files will be uploaded immediately after selection. If disabled, files upload on form submission.', 'la-addons' ) ); ?></p>
                        </fieldset>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row"><?php echo esc_html( __( 'User Interface', 'la-addons' ) ); ?></th>
                    <td>
                        <fieldset>
                            <label>
                                <input type="checkbox" 
                                       name="la_addons_dragdrop_upload_options[show_previews]" 
                                       value="1" 
                                       <?php checked( $options['show_previews'] ); ?> />
                                <?php echo esc_html( __( 'Show file previews/thumbnails', 'la-addons' ) ); ?>
                            </label><br>
                            
                            <label>
                                <input type="checkbox" 
                                       name="la_addons_dragdrop_upload_options[enable_progress]" 
                                       value="1" 
                                       <?php checked( $options['enable_progress'] ); ?> />
                                <?php echo esc_html( __( 'Show upload progress indicators', 'la-addons' ) ); ?>
                            </label>
                        </fieldset>
                    </td>
                </tr>
                
                <!-- File Management -->
                <tr>
                    <th scope="row">
                        <label for="dragdrop_duplicate_handling"><?php echo esc_html( __( 'Duplicate Files', 'la-addons' ) ); ?></label>
                    </th>
                    <td>
                        <select id="dragdrop_duplicate_handling" 
                                name="la_addons_dragdrop_upload_options[duplicate_handling]">
                            <option value="rename" <?php selected( $options['duplicate_handling'], 'rename' ); ?>>
                                <?php echo esc_html( __( 'Rename automatically', 'la-addons' ) ); ?>
                            </option>
                            <option value="replace" <?php selected( $options['duplicate_handling'], 'replace' ); ?>>
                                <?php echo esc_html( __( 'Replace existing file', 'la-addons' ) ); ?>
                            </option>
                            <option value="skip" <?php selected( $options['duplicate_handling'], 'skip' ); ?>>
                                <?php echo esc_html( __( 'Skip duplicate files', 'la-addons' ) ); ?>
                            </option>
                        </select>
                        <p class="description"><?php echo esc_html( __( 'How to handle files with the same name.', 'la-addons' ) ); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="dragdrop_file_naming"><?php echo esc_html( __( 'File Naming', 'la-addons' ) ); ?></label>
                    </th>
                    <td>
                        <select id="dragdrop_file_naming" 
                                name="la_addons_dragdrop_upload_options[file_naming]">
                            <option value="original" <?php selected( $options['file_naming'], 'original' ); ?>>
                                <?php echo esc_html( __( 'Keep original names', 'la-addons' ) ); ?>
                            </option>
                            <option value="timestamp" <?php selected( $options['file_naming'], 'timestamp' ); ?>>
                                <?php echo esc_html( __( 'Add timestamp', 'la-addons' ) ); ?>
                            </option>
                            <option value="custom" <?php selected( $options['file_naming'], 'custom' ); ?>>
                                <?php echo esc_html( __( 'Custom pattern', 'la-addons' ) ); ?>
                            </option>
                        </select>
                        
                        <div id="custom_naming_pattern" style="margin-top: 10px; <?php echo $options['file_naming'] !== 'custom' ? 'display: none;' : ''; ?>">
                            <input type="text" 
                                   name="la_addons_dragdrop_upload_options[custom_naming_pattern]" 
                                   value="<?php echo esc_attr( $options['custom_naming_pattern'] ); ?>" 
                                   class="regular-text" 
                                   placeholder="{name}-{timestamp}" />
                            <p class="description">
                                <?php echo esc_html( __( 'Available placeholders: {name}, {timestamp}, {random}, {form_id}, {field_name}', 'la-addons' ) ); ?>
                            </p>
                        </div>
                    </td>
                </tr>
                
                <!-- Security -->
                <tr>
                    <th scope="row"><?php echo esc_html( __( 'Security', 'la-addons' ) ); ?></th>
                    <td>
                        <fieldset>
                            <label>
                                <input type="checkbox" 
                                       name="la_addons_dragdrop_upload_options[security_scan]" 
                                       value="1" 
                                       <?php checked( $options['security_scan'] ); ?> />
                                <?php echo esc_html( __( 'Enable security scanning', 'la-addons' ) ); ?>
                            </label>
                            <p class="description"><?php echo esc_html( __( 'Scan uploaded files for potentially malicious content.', 'la-addons' ) ); ?></p>
                        </fieldset>
                    </td>
                </tr>
            </tbody>
        </table>
        
        <div class="la-addons-panel-footer">
            <p class="description">
                <strong><?php echo esc_html( __( 'Note:', 'la-addons' ) ); ?></strong>
                <?php echo esc_html( __( 'These settings apply to all drag-and-drop upload fields in this form. Individual field settings in the form editor will override these defaults.', 'la-addons' ) ); ?>
            </p>
        </div>
    </div>
    
    <script>
    jQuery(document).ready(function($) {
        // Show/hide custom naming pattern field
        $('#dragdrop_file_naming').change(function() {
            if ($(this).val() === 'custom') {
                $('#custom_naming_pattern').show();
            } else {
                $('#custom_naming_pattern').hide();
            }
        });
    });
    </script>
    
    <?php
}

/**
 * Save drag-and-drop upload options
 */
function la_addons_save_dragdrop_upload_options( $post ) {
    if ( isset( $_POST['la_addons_dragdrop_upload_options'] ) ) {
        $options = $_POST['la_addons_dragdrop_upload_options'];
        
        // Sanitize and validate options
        $sanitized_options = array(
            'max_files' => max( 1, min( 50, intval( $options['max_files'] ) ) ),
            'max_file_size' => sanitize_text_field( $options['max_file_size'] ),
            'allowed_types' => sanitize_textarea_field( $options['allowed_types'] ),
            'auto_upload' => isset( $options['auto_upload'] ) ? true : false,
            'show_previews' => isset( $options['show_previews'] ) ? true : false,
            'enable_progress' => isset( $options['enable_progress'] ) ? true : false,
            'duplicate_handling' => in_array( $options['duplicate_handling'], array( 'rename', 'replace', 'skip' ) ) 
                ? $options['duplicate_handling'] : 'rename',
            'file_naming' => in_array( $options['file_naming'], array( 'original', 'timestamp', 'custom' ) ) 
                ? $options['file_naming'] : 'original',
            'custom_naming_pattern' => sanitize_text_field( $options['custom_naming_pattern'] ),
            'security_scan' => isset( $options['security_scan'] ) ? true : false
        );
        
        update_post_meta( $post->id(), '_la_addons_dragdrop_upload_options', $sanitized_options );
        
        error_log( 'LA Addons: Drag-drop upload options saved for form ' . $post->id() );
    }
}
add_action( 'wpcf7_save_contact_form', 'la_addons_save_dragdrop_upload_options' );

/**
 * Get drag-and-drop upload options for a form
 */
function la_addons_get_dragdrop_upload_options( $form_id ) {
    $options = get_post_meta( $form_id, '_la_addons_dragdrop_upload_options', true );
    
    $defaults = array(
        'max_files' => 5,
        'max_file_size' => '10MB',
        'allowed_types' => 'image/*,application/pdf,.doc,.docx,.txt',
        'auto_upload' => true,
        'show_previews' => true,
        'enable_progress' => true,
        'duplicate_handling' => 'rename',
        'file_naming' => 'original',
        'custom_naming_pattern' => '{name}-{timestamp}',
        'security_scan' => true
    );
    
    return wp_parse_args( $options, $defaults );
}
