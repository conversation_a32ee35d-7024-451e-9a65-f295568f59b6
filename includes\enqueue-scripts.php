<?php

function la_addons_enqueue_scripts() {
    // Enqueue conditional fields CSS and JS on frontend
    wp_enqueue_style(
        'la-addons-frontend-style',
        plugin_dir_url( __FILE__ ) . '../assets/css/admin-style.css',
        array(),
        '1.0.0'
    );

    wp_enqueue_script(
        'la-addons-conditional-fields-frontend',
        plugin_dir_url( __FILE__ ) . '../assets/js/conditional-fields.js',
        array(),
        '1.0.0',
        true
    );

    // Localize script with AJAX URL and nonce for conditional logic
    wp_localize_script(
        'la-addons-conditional-fields-frontend',
        'laAddonsConditionalLogic',
        array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('la_addons_conditional_logic_nonce'),
            'restUrl' => rest_url(),
            'restNonce' => wp_create_nonce('wp_rest')
        )
    );

    wp_enqueue_script(
        'la-addons-signature-pad-lib',
        plugin_dir_url( __FILE__ ) . '../assets/js/signature_pad.min.js',
        array(),
        '1.5.3',
        true
    );

    wp_enqueue_script(
        'la-addons-signature-pad-init',
        plugin_dir_url( __FILE__ ) . '../assets/js/signature-pad-init.js',
        array( 'la-addons-signature-pad-lib' ),
        '1.0',
        true
    );

    // Add AJAX URL for signature options
    wp_localize_script(
        'la-addons-signature-pad-init',
        'laAddonsSignature',
        array(
            'ajaxUrl' => admin_url( 'admin-ajax.php' )
        )
    );

    // Enqueue drag-and-drop upload scripts
    wp_enqueue_script(
        'la-addons-dragdrop-upload',
        plugin_dir_url( __FILE__ ) . '../assets/js/drag-drop-upload.js',
        array(),
        '1.0.0',
        true
    );

    // Localize script for drag-and-drop upload
    wp_localize_script(
        'la-addons-dragdrop-upload',
        'laAddonsDragDrop',
        array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('la_addons_dragdrop_upload'),
            'maxFileSize' => wp_max_upload_size(),
            'allowedTypes' => get_allowed_mime_types()
        )
    );
}
add_action( 'wp_enqueue_scripts', 'la_addons_enqueue_scripts' );

// Add AJAX handler for signature options
function la_addons_get_signature_options() {
    $form_id = isset( $_POST['form_id'] ) ? intval( $_POST['form_id'] ) : 0;
    
    if ( $form_id ) {
        $options = get_post_meta( $form_id, '_la_addons_signature_options', true );
        
        if ( empty( $options ) ) {
            $options = array(
                'pad_bg_color' => '#dddddd',
                'pen_color' => '#000000',
                'pad_width' => 300,
                'pad_height' => 100
            );
        } else {
            // Ensure numeric values are properly formatted
            if (isset($options['pad_width'])) {
                $options['pad_width'] = intval($options['pad_width']);
            }
            
            if (isset($options['pad_height'])) {
                $options['pad_height'] = intval($options['pad_height']);
            }
        }
        
        error_log('Signature options for form ' . $form_id . ': ' . print_r($options, true));
        wp_send_json_success( $options );
    } else {
        error_log('No form ID provided for signature options');
        wp_send_json_error( array('message' => 'No form ID provided') );
    }
    
    wp_die();
}
add_action( 'wp_ajax_get_signature_options', 'la_addons_get_signature_options' );
add_action( 'wp_ajax_nopriv_get_signature_options', 'la_addons_get_signature_options' );

function la_addons_admin_enqueue_scripts() {
    $screen = get_current_screen();
    
    // Only load on CF7 edit screen
    if ( $screen->id === 'toplevel_page_wpcf7' || $screen->id === 'contact_page_wpcf7-new' ) {
        wp_enqueue_style( 'la-addons-admin-style', plugin_dir_url( dirname( __FILE__ ) ) . 'assets/css/admin-style.css', array(), '1.0.0' );
        wp_enqueue_script( 'la-addons-conditional-fields', plugin_dir_url( dirname( __FILE__ ) ) . 'assets/js/conditional-fields.js', array( 'jquery' ), '1.0.0', true );
        wp_enqueue_script( 'la-addons-signature-options', plugin_dir_url( dirname( __FILE__ ) ) . 'assets/js/signature-options.js', array( 'jquery' ), '1.0.0', true );
        wp_enqueue_script( 'la-addons-dragdrop-tag-generator', plugin_dir_url( dirname( __FILE__ ) ) . 'assets/js/dragdrop-tag-generator.js', array( 'jquery' ), '1.0.0', true );
    }
    
    // Only load on LA Addons submissions page
    if ( $screen->id === 'toplevel_page_la-addons' ) {
        wp_enqueue_style( 'la-addons-admin-style', plugin_dir_url( dirname( __FILE__ ) ) . 'assets/css/admin-style.css', array(), '1.0.0' );
    }
}
add_action( 'admin_enqueue_scripts', 'la_addons_admin_enqueue_scripts' );

function la_addons_enqueue_signature_admin_scripts( $hook_suffix ) {
    if ( 'post.php' === $hook_suffix || 'post-new.php' === $hook_suffix ) {
        $post_type = get_post_type();
        if ( 'wpcf7_contact_form' === $post_type ) {
            wp_enqueue_style(
                'la-addons-admin-style',
                plugin_dir_url( __FILE__ ) . '../assets/css/admin-style.css',
                array(),
                '1.0'
            );
            
            wp_enqueue_script(
                'la-addons-signature-pad-lib',
                plugin_dir_url( __FILE__ ) . '../assets/js/signature_pad.min.js',
                array(),
                '1.5.3',
                true
            );

            wp_enqueue_script(
                'la-addons-signature-pad-init',
                plugin_dir_url( __FILE__ ) . '../assets/js/signature-pad-init.js',
                array( 'la-addons-signature-pad-lib' ),
                '1.0',
                true
            );
            
            // Add AJAX URL for signature options
            wp_localize_script(
                'la-addons-signature-pad-init',
                'laAddonsSignature',
                array(
                    'ajaxUrl' => admin_url( 'admin-ajax.php' )
                )
            );
        }
    }
}
add_action( 'admin_enqueue_scripts', 'la_addons_enqueue_signature_admin_scripts' );








